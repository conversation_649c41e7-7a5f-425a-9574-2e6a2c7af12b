'use client'

import Link from 'next/link'
import { ChatBubbleBottomCenterTextIcon } from '@heroicons/react/24/outline'
import { isEmpty } from 'lodash'
import { usePathname, useSearchParams } from 'next/navigation'
import { formatDate } from '@/app/helpers/dateHelper'
import { List } from '@/components/skeletons'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { Button } from '@/components/ui/button'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { useState, useEffect } from 'react'

const CrewAllocatedTasks = ({ taskList }: { taskList?: any }) => {
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const [filteredTaskList, setFilteredTaskList] = useState(taskList)
    const [filters, setFilters] = useState<{
        vessel: any
        status: any
        keyword: any
    }>({
        vessel: null,
        status: null,
        keyword: null,
    })

    // Update filtered list when taskList changes
    useEffect(() => {
        setFilteredTaskList(taskList)
    }, [taskList])

    // Apply all filters whenever filters or taskList changes
    useEffect(() => {
        if (!taskList) return

        let filtered = [...taskList]

        // Apply vessel filter - following the same pattern as maintenance list
        if (filters.vessel) {
            console.log(
                'Applying vessel filter. filters.vessel:',
                filters.vessel,
            )
            console.log('Is array?', Array.isArray(filters.vessel))

            // Handle both single and multi-select vessel filtering
            let vesselIds: number[] = []
            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {
                vesselIds = filters.vessel.map((item: any) => +item.value)
                console.log('Multi-select vessel IDs:', vesselIds)
            } else if (filters.vessel && !Array.isArray(filters.vessel)) {
                vesselIds = [+filters.vessel.value]
                console.log('Single-select vessel ID:', vesselIds)
            }

            if (vesselIds.length > 0) {
                console.log(
                    'Sample task basicComponent:',
                    taskList[0]?.basicComponent,
                )
                filtered = filtered.filter((task: any) => {
                    const match = vesselIds.includes(task?.basicComponent?.id)
                    if (!match) {
                        console.log(
                            `Task ${task.name} - basicComponent.id: ${task?.basicComponent?.id}, not in vesselIds: ${vesselIds}`,
                        )
                    }
                    return match
                })
                console.log(
                    `Filtered ${filtered.length} tasks from ${taskList.length} total`,
                )
            }
        }

        // Apply status filter
        if (filters.status) {
            filtered = filtered.filter(
                (task: any) => task?.status === filters.status.value,
            )
        }

        // Apply keyword filter
        if (filters.keyword && filters.keyword.value) {
            const keyword = filters.keyword.value.toLowerCase()
            filtered = filtered.filter(
                (task: any) =>
                    task?.name?.toLowerCase().includes(keyword) ||
                    task?.description?.toLowerCase().includes(keyword) ||
                    task?.comments?.toLowerCase().includes(keyword),
            )
        }

        setFilteredTaskList(filtered)
    }, [taskList, filters])

    // Handle filter changes
    const handleFilterOnChange = ({ type, data }: any) => {
        console.log(`Filter change - type: ${type}, data:`, data)
        setFilters((prev) => ({
            ...prev,
            [type]: data,
        }))
    }

    // Define columns for the DataTable
    const columns: ExtendedColumnDef<any, any>[] = [
        {
            accessorKey: 'title',
            header: 'Task',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const task = row.original
                return (
                    <div>
                        <div className="flex items-center justify-between">
                            <span className="text-foreground flex items-center">
                                <Link
                                    href={`/maintenance?taskID=${task.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                    className="focus:outline-none">
                                    {task.name}
                                </Link>
                                <div
                                    className={`inline-block rounded px-3 py-1 ml-3 ${
                                        task?.isOverDue?.status === 'High'
                                            ? 'alert'
                                            : ''
                                    } ${
                                        task?.isOverDue?.status === 'Low' ||
                                        task?.isOverDue?.status ===
                                            'Upcoming' ||
                                        task?.isOverDue?.status === 'Completed'
                                            ? 'success'
                                            : ''
                                    } ${
                                        task?.isOverDue?.status === 'Medium' ||
                                        task?.isOverDue?.days ===
                                            'Save As Draft'
                                            ? 'text-yellow-600 bg-yellow-100'
                                            : ''
                                    }`}>
                                    {task?.isOverDue?.status &&
                                        ['High', 'Medium', 'Low'].includes(
                                            task.isOverDue.status,
                                        ) &&
                                        task?.isOverDue?.days}
                                    {task?.isOverDue?.status === 'Completed' &&
                                        task?.isOverDue?.days ===
                                            'Save As Draft' &&
                                        task?.isOverDue?.days}
                                    {task?.isOverDue?.status === 'Upcoming' &&
                                        task?.isOverDue?.days}
                                    {task?.isOverDue?.status === 'Completed' &&
                                        isEmpty(task?.isOverDue?.days) &&
                                        task?.isOverDue?.status}
                                    {task?.isOverDue?.status === 'Completed' &&
                                        !isEmpty(task?.isOverDue?.days) &&
                                        task?.isOverDue?.days !==
                                            'Save As Draft' &&
                                        task?.isOverDue?.days}
                                </div>
                            </span>
                            <div className="w-14 flex items-center pl-1">
                                {task.Comments !== null && (
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="outline-none px-1">
                                                <ChatBubbleBottomCenterTextIcon className="w-5 h-5" />
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-64">
                                            <div className="leading-loose">
                                                {task.comments}
                                            </div>
                                        </PopoverContent>
                                    </Popover>
                                )}
                            </div>
                        </div>
                        {task.description && (
                            <div className="mt-1 text-sm text-muted-foreground">
                                {task.description}
                            </div>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'vessel',
            header: 'Vessel',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const task = row.original
                return (
                    <div className="text-center">
                        {task?.basicComponent?.title || 'N/A'}
                    </div>
                )
            },
        },
        {
            accessorKey: 'inventoryItem',
            header: 'Inventory Item',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const task = row.original
                // Check for inventory from multiple possible sources
                const inventoryItem =
                    task?.inventory?.item ||
                    task?.maintenanceSchedule?.inventory?.item ||
                    null

                return (
                    <div className="text-center">{inventoryItem || 'N/A'}</div>
                )
            },
        },
        {
            accessorKey: 'status',
            header: 'Status',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const task = row.original
                return (
                    <div className="text-right">
                        <div className="text-sm text-muted-foreground mb-1">
                            Due: {formatDate(task.expires)}
                        </div>
                        <div
                            className={`inline-block rounded px-3 py-1 ${
                                task.status == 'Completed' ? 'success' : 'alert'
                            }`}>
                            <span>{task.status}</span>
                        </div>
                    </div>
                )
            },
        },
    ]

    return (
        <>
            {!taskList ? (
                <List />
            ) : (
                <DataTable
                    columns={columns}
                    data={filteredTaskList || []}
                    showToolbar={true}
                    pageSize={50}
                    showPageSizeSelector={false}
                    onChange={handleFilterOnChange}
                />
            )}
        </>
    )
}

export default CrewAllocatedTasks
