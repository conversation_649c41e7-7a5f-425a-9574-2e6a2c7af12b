'use client'

import Link from 'next/link'
import { ChatBubbleBottomCenterTextIcon } from '@heroicons/react/24/outline'
import { isEmpty } from 'lodash'
import { usePathname, useSearchParams } from 'next/navigation'
import { formatDate } from '@/app/helpers/dateHelper'
import { List } from '@/components/skeletons'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { Button } from '@/components/ui/button'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { useState, useEffect } from 'react'

const CrewAllocatedTasks = ({ taskList }: { taskList?: any }) => {
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const [filteredTaskList, setFilteredTaskList] = useState(taskList)
    const [filters, setFilters] = useState<{
        vessel: any
        status: any
        keyword: any
    }>({
        vessel: null,
        status: null,
        keyword: null,
    })

    // Get crew member ID from URL params for debugging
    const crewId = searchParams.get('id')

    // Debug crew member filtering
    useEffect(() => {
        if (taskList && taskList.length > 0) {
            console.log(`Crew ID from URL: ${crewId}`)
            console.log(`Total tasks received: ${taskList.length}`)
            console.log('Sample task assignedToID:', taskList[0]?.assignedToID)
            console.log('All unique assignedToIDs:', [
                ...new Set(taskList.map((t: any) => t.assignedToID)),
            ])
        }
    }, [taskList, crewId])

    // Update filtered list when taskList changes
    useEffect(() => {
        setFilteredTaskList(taskList)
    }, [taskList])

    // Apply all filters whenever filters or taskList changes
    useEffect(() => {
        if (!taskList) return

        let filtered = [...taskList]

        // Apply vessel filter - following the same pattern as maintenance list
        if (filters.vessel) {
            // Handle both single and multi-select vessel filtering
            let vesselIds: string[] = []
            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {
                vesselIds = filters.vessel.map((item: any) =>
                    String(item.value),
                )
            } else if (filters.vessel && !Array.isArray(filters.vessel)) {
                vesselIds = [String(filters.vessel.value)]
            }

            if (vesselIds.length > 0) {
                filtered = filtered.filter((task: any) => {
                    const taskVesselId = String(task?.basicComponent?.id)
                    return vesselIds.includes(taskVesselId)
                })
            }
        }

        // Apply status filter
        if (filters.status) {
            filtered = filtered.filter(
                (task: any) => task?.status === filters.status.value,
            )
        }

        // Apply keyword filter
        if (
            filters.keyword &&
            filters.keyword.value &&
            filters.keyword.value.trim()
        ) {
            const keyword = filters.keyword.value.toLowerCase().trim()
            console.log(`Applying keyword filter: "${keyword}"`)
            const beforeCount = filtered.length
            filtered = filtered.filter((task: any) => {
                // Safely get text content, handling null/undefined and HTML
                const getName = () => (task?.name || '').toLowerCase()
                const getDescription = () =>
                    (task?.description || '').toLowerCase()
                const getComments = () => {
                    if (!task?.comments) return ''
                    // Strip HTML tags if present and convert to lowercase
                    return task.comments.replace(/<[^>]*>/g, '').toLowerCase()
                }

                const nameMatch = getName().includes(keyword)
                const descMatch = getDescription().includes(keyword)
                const commentMatch = getComments().includes(keyword)
                const match = nameMatch || descMatch || commentMatch

                if (match) {
                    console.log(
                        `Task "${task.name}" matches keyword "${keyword}"`,
                    )
                }
                return match
            })
            console.log(
                `Keyword filter: ${beforeCount} -> ${filtered.length} tasks`,
            )
        }

        setFilteredTaskList(filtered)
    }, [taskList, filters])

    // Handle filter changes
    const handleFilterOnChange = ({ type, data }: any) => {
        console.log(`Filter change - type: ${type}, data:`, data)
        if (type === 'keyword') {
            console.log('Keyword filter data structure:', data)
            console.log('Keyword value:', data?.value)
        }
        setFilters((prev) => ({
            ...prev,
            [type]: data,
        }))
    }

    // Define columns for the DataTable
    const columns: ExtendedColumnDef<any, any>[] = [
        {
            accessorKey: 'title',
            header: 'Task',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const task = row.original
                return (
                    <div>
                        <div className="flex items-center justify-between">
                            <span className="text-foreground flex items-center">
                                <Link
                                    href={`/maintenance?taskID=${task.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                    className="focus:outline-none">
                                    {task.name}
                                </Link>
                                <div
                                    className={`inline-block rounded px-3 py-1 ml-3 ${
                                        task?.isOverDue?.status === 'High'
                                            ? 'alert'
                                            : ''
                                    } ${
                                        task?.isOverDue?.status === 'Low' ||
                                        task?.isOverDue?.status ===
                                            'Upcoming' ||
                                        task?.isOverDue?.status === 'Completed'
                                            ? 'success'
                                            : ''
                                    } ${
                                        task?.isOverDue?.status === 'Medium' ||
                                        task?.isOverDue?.days ===
                                            'Save As Draft'
                                            ? 'text-yellow-600 bg-yellow-100'
                                            : ''
                                    }`}>
                                    {task?.isOverDue?.status &&
                                        ['High', 'Medium', 'Low'].includes(
                                            task.isOverDue.status,
                                        ) &&
                                        task?.isOverDue?.days}
                                    {task?.isOverDue?.status === 'Completed' &&
                                        task?.isOverDue?.days ===
                                            'Save As Draft' &&
                                        task?.isOverDue?.days}
                                    {task?.isOverDue?.status === 'Upcoming' &&
                                        task?.isOverDue?.days}
                                    {task?.isOverDue?.status === 'Completed' &&
                                        isEmpty(task?.isOverDue?.days) &&
                                        task?.isOverDue?.status}
                                    {task?.isOverDue?.status === 'Completed' &&
                                        !isEmpty(task?.isOverDue?.days) &&
                                        task?.isOverDue?.days !==
                                            'Save As Draft' &&
                                        task?.isOverDue?.days}
                                </div>
                            </span>
                            <div className="w-14 flex items-center pl-1">
                                {task.Comments !== null && (
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="outline-none px-1">
                                                <ChatBubbleBottomCenterTextIcon className="w-5 h-5" />
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-64">
                                            <div className="leading-loose">
                                                {task.comments}
                                            </div>
                                        </PopoverContent>
                                    </Popover>
                                )}
                            </div>
                        </div>
                        {task.description && (
                            <div className="mt-1 text-sm text-muted-foreground">
                                {task.description}
                            </div>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'vessel',
            header: 'Vessel',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const task = row.original
                return (
                    <div className="text-center">
                        {task?.basicComponent?.title || 'N/A'}
                    </div>
                )
            },
        },
        {
            accessorKey: 'inventoryItem',
            header: 'Inventory Item',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const task = row.original
                // Check for inventory from multiple possible sources
                const inventoryItem =
                    task?.inventory?.item ||
                    task?.maintenanceSchedule?.inventory?.item ||
                    null

                return (
                    <div className="text-center">{inventoryItem || 'N/A'}</div>
                )
            },
        },
        {
            accessorKey: 'status',
            header: 'Status',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const task = row.original
                return (
                    <div className="text-right">
                        <div className="text-sm text-muted-foreground mb-1">
                            Due: {formatDate(task.expires)}
                        </div>
                        <div
                            className={`inline-block rounded px-3 py-1 ${
                                task.status == 'Completed' ? 'success' : 'alert'
                            }`}>
                            <span>{task.status}</span>
                        </div>
                    </div>
                )
            },
        },
    ]

    return (
        <>
            {!taskList ? (
                <List />
            ) : (
                <DataTable
                    columns={columns}
                    data={filteredTaskList || []}
                    showToolbar={true}
                    pageSize={50}
                    showPageSizeSelector={false}
                    onChange={handleFilterOnChange}
                />
            )}
        </>
    )
}

export default CrewAllocatedTasks
